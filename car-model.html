<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D汽车模型</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: Arial, sans-serif;
            overflow: hidden;
        }
        
        #container {
            width: 100vw;
            height: 100vh;
            position: relative;
        }
        
        #info {
            position: absolute;
            top: 20px;
            left: 20px;
            color: white;
            z-index: 100;
            background: rgba(0, 0, 0, 0.7);
            padding: 15px;
            border-radius: 8px;
            font-size: 14px;
            max-width: 300px;
        }
        
        #controls {
            position: absolute;
            bottom: 20px;
            left: 20px;
            color: white;
            z-index: 100;
            background: rgba(0, 0, 0, 0.7);
            padding: 15px;
            border-radius: 8px;
        }
        
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 8px 16px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
            transition: background 0.3s;
        }
        
        button:hover {
            background: #45a049;
        }
        
        #colorPicker {
            position: absolute;
            bottom: 20px;
            right: 20px;
            color: white;
            z-index: 100;
            background: rgba(0, 0, 0, 0.7);
            padding: 15px;
            border-radius: 8px;
        }
        
        input[type="color"] {
            width: 40px;
            height: 40px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        
        #loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 18px;
            z-index: 200;
        }
    </style>
</head>
<body>
    <div id="container">
        <div id="loading">加载中...</div>
        <div id="info">
            <h3>3D汽车模型</h3>
            <p>• 完整的汽车结构设计</p>
            <p>• 鼠标拖拽旋转视角</p>
            <p>• 滚轮缩放</p>
            <p>• 实时光照和阴影</p>
            <p>• 可自定义车身颜色</p>
        </div>
        <div id="controls">
            <button onclick="toggleRotation()">自动旋转</button>
            <button onclick="resetView()">重置视角</button>
            <button onclick="toggleWireframe()">线框模式</button>
            <button onclick="toggleLights()">车灯开关</button>
            <button onclick="openDoors()">开关车门</button>
        </div>
        <div id="colorPicker">
            <h4>车身颜色:</h4>
            <input type="color" id="bodyColor" value="#ff0000" onchange="changeCarColor(this.value)">
            <input type="color" id="roofColor" value="#000000" onchange="changeRoofColor(this.value)">
            <br>
            <label style="font-size: 12px;">车身 | 车顶</label>
        </div>
    </div>

    <script type="importmap">
    {
        "imports": {
            "three": "https://unpkg.com/three@0.155.0/build/three.module.js",
            "three/addons/": "https://unpkg.com/three@0.155.0/examples/jsm/"
        }
    }
    </script>
    
    <script type="module">
        import * as THREE from 'three';
        import { OrbitControls } from 'three/addons/controls/OrbitControls.js';
        
        let scene, camera, renderer, car, carGroup;
        let controls;
        let isRotating = false;
        let isWireframe = false;
        let lightsOn = false;
        let doorsOpen = false;
        
        // 汽车部件
        let carBody, carRoof, wheels = [], doors = [], headlights = [];
        
        function init() {
            // 创建场景
            scene = new THREE.Scene();
            scene.fog = new THREE.Fog(0x667eea, 10, 50);
            
            // 创建相机
            camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
            camera.position.set(5, 3, 5);
            
            // 创建渲染器
            renderer = new THREE.WebGLRenderer({ antialias: true });
            renderer.setSize(window.innerWidth, window.innerHeight);
            renderer.shadowMap.enabled = true;
            renderer.shadowMap.type = THREE.PCFSoftShadowMap;
            renderer.setClearColor(0x667eea, 1);
            renderer.physicallyCorrectLights = true;
            document.getElementById('container').appendChild(renderer.domElement);
            
            // 创建控制器
            controls = new OrbitControls(camera, renderer.domElement);
            controls.enableDamping = true;
            controls.dampingFactor = 0.05;
            controls.minDistance = 3;
            controls.maxDistance = 20;
            controls.maxPolarAngle = Math.PI / 2;
            
            // 创建汽车
            createCar();
            
            // 创建环境
            createEnvironment();
            
            // 创建光照
            createLights();
            
            // 开始渲染
            animate();
            
            // 隐藏加载提示
            document.getElementById('loading').style.display = 'none';
        }
        
        function createCar() {
            carGroup = new THREE.Group();
            
            // 创建车身主体
            createCarBody();
            
            // 创建车顶
            createCarRoof();
            
            // 创建轮子
            createWheels();
            
            // 创建车窗
            createWindows();
            
            // 创建车门
            createDoors();
            
            // 创建车灯
            createHeadlights();
            
            // 创建细节
            createDetails();
            
            scene.add(carGroup);
        }
        
        function createCarBody() {
            // 使用更复杂的几何体创建真实的车身形状
            const bodyShape = new THREE.Shape();

            // 创建车身轮廓 - 更流线型的设计
            bodyShape.moveTo(-2, 0);
            bodyShape.lineTo(-1.8, 0.3);
            bodyShape.quadraticCurveTo(-1.5, 0.8, -1, 0.9);
            bodyShape.lineTo(1, 0.9);
            bodyShape.quadraticCurveTo(1.5, 0.8, 1.8, 0.3);
            bodyShape.lineTo(2, 0);
            bodyShape.lineTo(2, -0.4);
            bodyShape.lineTo(-2, -0.4);
            bodyShape.lineTo(-2, 0);

            const extrudeSettings = {
                depth: 1.8,
                bevelEnabled: true,
                bevelSegments: 8,
                steps: 2,
                bevelSize: 0.05,
                bevelThickness: 0.05
            };

            const bodyGeometry = new THREE.ExtrudeGeometry(bodyShape, extrudeSettings);
            const bodyMaterial = new THREE.MeshPhysicalMaterial({
                color: 0xff0000,
                metalness: 0.8,
                roughness: 0.2,
                clearcoat: 1.0,
                clearcoatRoughness: 0.1,
                reflectivity: 0.9
            });

            carBody = new THREE.Mesh(bodyGeometry, bodyMaterial);
            carBody.position.set(0, 0.4, -0.9);
            carBody.castShadow = true;
            carBody.receiveShadow = true;
            carGroup.add(carBody);

            // 添加引擎盖线条
            const hoodLineGeometry = new THREE.BoxGeometry(1.5, 0.02, 1.6);
            const hoodLineMaterial = new THREE.MeshPhongMaterial({ color: 0x333333 });
            const hoodLine = new THREE.Mesh(hoodLineGeometry, hoodLineMaterial);
            hoodLine.position.set(0.8, 0.85, 0);
            carGroup.add(hoodLine);
        }
        
        function createCarRoof() {
            // 创建更真实的车顶形状
            const roofShape = new THREE.Shape();
            roofShape.moveTo(-1.2, 0);
            roofShape.quadraticCurveTo(-1.2, 0.4, -0.8, 0.4);
            roofShape.lineTo(0.8, 0.4);
            roofShape.quadraticCurveTo(1.2, 0.4, 1.2, 0);
            roofShape.lineTo(1.2, -0.4);
            roofShape.quadraticCurveTo(1.2, -0.8, 0.8, -0.8);
            roofShape.lineTo(-0.8, -0.8);
            roofShape.quadraticCurveTo(-1.2, -0.8, -1.2, -0.4);
            roofShape.lineTo(-1.2, 0);

            const roofExtrudeSettings = {
                depth: 1.5,
                bevelEnabled: true,
                bevelSegments: 6,
                steps: 1,
                bevelSize: 0.03,
                bevelThickness: 0.03
            };

            const roofGeometry = new THREE.ExtrudeGeometry(roofShape, roofExtrudeSettings);
            const roofMaterial = new THREE.MeshPhysicalMaterial({
                color: 0x000000,
                metalness: 0.9,
                roughness: 0.1,
                clearcoat: 1.0,
                clearcoatRoughness: 0.05
            });

            carRoof = new THREE.Mesh(roofGeometry, roofMaterial);
            carRoof.position.set(-0.2, 1.2, -0.75);
            carRoof.castShadow = true;
            carGroup.add(carRoof);

            // 添加天窗
            const sunroofGeometry = new THREE.PlaneGeometry(0.8, 1.0);
            const sunroofMaterial = new THREE.MeshPhysicalMaterial({
                color: 0x87CEEB,
                transparent: true,
                opacity: 0.4,
                metalness: 0.1,
                roughness: 0.1
            });
            const sunroof = new THREE.Mesh(sunroofGeometry, sunroofMaterial);
            sunroof.position.set(-0.2, 1.61, 0);
            sunroof.rotation.x = -Math.PI / 2;
            carGroup.add(sunroof);
        }
        
        function createWheels() {
            const wheelPositions = [
                { x: 1.4, y: 0, z: 1.2 },
                { x: 1.4, y: 0, z: -1.2 },
                { x: -1.4, y: 0, z: 1.2 },
                { x: -1.4, y: 0, z: -1.2 }
            ];

            wheelPositions.forEach((pos, index) => {
                const wheelGroup = new THREE.Group();

                // 轮胎 - 更真实的轮胎纹理
                const wheelGeometry = new THREE.CylinderGeometry(0.45, 0.45, 0.25, 32);
                const wheelMaterial = new THREE.MeshPhysicalMaterial({
                    color: 0x1a1a1a,
                    roughness: 0.9,
                    metalness: 0.1
                });
                const wheel = new THREE.Mesh(wheelGeometry, wheelMaterial);
                wheel.rotation.z = Math.PI / 2;
                wheel.castShadow = true;
                wheelGroup.add(wheel);

                // 轮胎侧壁纹理
                const sidewallGeometry = new THREE.CylinderGeometry(0.44, 0.44, 0.26, 32);
                const sidewallMaterial = new THREE.MeshPhongMaterial({
                    color: 0x2a2a2a,
                    bumpScale: 0.02
                });
                const sidewall = new THREE.Mesh(sidewallGeometry, sidewallMaterial);
                sidewall.rotation.z = Math.PI / 2;
                wheelGroup.add(sidewall);

                // 高级轮毂设计
                const rimGeometry = new THREE.CylinderGeometry(0.32, 0.32, 0.28, 16);
                const rimMaterial = new THREE.MeshPhysicalMaterial({
                    color: 0xc0c0c0,
                    metalness: 0.9,
                    roughness: 0.1,
                    clearcoat: 1.0
                });
                const rim = new THREE.Mesh(rimGeometry, rimMaterial);
                rim.rotation.z = Math.PI / 2;
                wheelGroup.add(rim);

                // 轮毂辐条
                for (let i = 0; i < 5; i++) {
                    const spokeGeometry = new THREE.BoxGeometry(0.25, 0.03, 0.3);
                    const spokeMaterial = new THREE.MeshPhysicalMaterial({
                        color: 0xa0a0a0,
                        metalness: 0.8,
                        roughness: 0.2
                    });
                    const spoke = new THREE.Mesh(spokeGeometry, spokeMaterial);
                    spoke.rotation.z = (i * Math.PI * 2) / 5;
                    spoke.position.z = pos.z > 0 ? 0.01 : -0.01;
                    wheelGroup.add(spoke);
                }

                // 刹车盘
                const brakeDiscGeometry = new THREE.CylinderGeometry(0.28, 0.28, 0.02, 32);
                const brakeDiscMaterial = new THREE.MeshPhysicalMaterial({
                    color: 0x666666,
                    metalness: 0.7,
                    roughness: 0.3
                });
                const brakeDisc = new THREE.Mesh(brakeDiscGeometry, brakeDiscMaterial);
                brakeDisc.rotation.z = Math.PI / 2;
                brakeDisc.position.z = pos.z > 0 ? 0.15 : -0.15;
                wheelGroup.add(brakeDisc);

                wheelGroup.position.set(pos.x, pos.y, pos.z);
                wheels.push(wheelGroup);
                carGroup.add(wheelGroup);
            });
        }
        
        function createWindows() {
            const windowMaterial = new THREE.MeshPhysicalMaterial({
                color: 0x87CEEB,
                transparent: true,
                opacity: 0.15,
                metalness: 0.0,
                roughness: 0.0,
                transmission: 0.9,
                thickness: 0.01,
                ior: 1.5,
                side: THREE.DoubleSide
            });

            // 前挡风玻璃 - 弯曲设计
            const frontWindowGeometry = new THREE.CylinderGeometry(1.2, 1.2, 1.5, 32, 1, true, 0, Math.PI);
            const frontWindow = new THREE.Mesh(frontWindowGeometry, windowMaterial);
            frontWindow.position.set(0.9, 1.3, 0);
            frontWindow.rotation.z = Math.PI / 2;
            frontWindow.rotation.y = Math.PI / 2;
            carGroup.add(frontWindow);

            // 后挡风玻璃 - 弯曲设计
            const rearWindowGeometry = new THREE.CylinderGeometry(1.0, 1.0, 1.3, 32, 1, true, 0, Math.PI);
            const rearWindow = new THREE.Mesh(rearWindowGeometry, windowMaterial);
            rearWindow.position.set(-1.1, 1.3, 0);
            rearWindow.rotation.z = -Math.PI / 2;
            rearWindow.rotation.y = Math.PI / 2;
            carGroup.add(rearWindow);

            // 侧窗 - 更真实的形状
            const sideWindowShape = new THREE.Shape();
            sideWindowShape.moveTo(-0.6, 0);
            sideWindowShape.lineTo(0.6, 0);
            sideWindowShape.quadraticCurveTo(0.7, 0.1, 0.6, 0.5);
            sideWindowShape.lineTo(-0.5, 0.5);
            sideWindowShape.quadraticCurveTo(-0.6, 0.4, -0.6, 0);

            const sideWindowGeometry = new THREE.ShapeGeometry(sideWindowShape);

            const leftWindow = new THREE.Mesh(sideWindowGeometry, windowMaterial);
            leftWindow.position.set(-0.2, 1.3, 0.82);
            carGroup.add(leftWindow);

            const rightWindow = new THREE.Mesh(sideWindowGeometry, windowMaterial);
            rightWindow.position.set(-0.2, 1.3, -0.82);
            rightWindow.rotation.y = Math.PI;
            carGroup.add(rightWindow);

            // 车窗框架
            const frameMaterial = new THREE.MeshPhongMaterial({ color: 0x333333 });
            const frameGeometry = new THREE.BoxGeometry(1.3, 0.02, 0.02);

            // 前窗框架
            const frontFrame = new THREE.Mesh(frameGeometry, frameMaterial);
            frontFrame.position.set(0.9, 1.0, 0);
            carGroup.add(frontFrame);

            // 后窗框架
            const rearFrame = new THREE.Mesh(frameGeometry, frameMaterial);
            rearFrame.position.set(-1.1, 1.0, 0);
            carGroup.add(rearFrame);
        }

        function createDoors() {
            const doorGeometry = new THREE.BoxGeometry(1.2, 0.8, 0.1);
            const doorMaterial = new THREE.MeshPhongMaterial({ color: 0xff0000 });

            // 左门
            const leftDoor = new THREE.Mesh(doorGeometry, doorMaterial);
            leftDoor.position.set(0.3, 0.4, 0.95);
            leftDoor.castShadow = true;
            doors.push(leftDoor);
            carGroup.add(leftDoor);

            // 右门
            const rightDoor = new THREE.Mesh(doorGeometry, doorMaterial);
            rightDoor.position.set(0.3, 0.4, -0.95);
            rightDoor.castShadow = true;
            doors.push(rightDoor);
            carGroup.add(rightDoor);
        }

        function createHeadlights() {
            // 现代LED大灯设计
            const headlightHousingGeometry = new THREE.CylinderGeometry(0.2, 0.18, 0.15, 16);
            const headlightHousingMaterial = new THREE.MeshPhysicalMaterial({
                color: 0x333333,
                metalness: 0.8,
                roughness: 0.2
            });

            // LED灯条
            const ledGeometry = new THREE.BoxGeometry(0.3, 0.02, 0.02);
            const ledMaterial = new THREE.MeshPhongMaterial({
                color: 0xffffff,
                emissive: 0x444444,
                emissiveIntensity: 0.3
            });

            // 左前灯组
            const leftHeadlightGroup = new THREE.Group();
            const leftHousing = new THREE.Mesh(headlightHousingGeometry, headlightHousingMaterial);
            leftHousing.rotation.z = Math.PI / 2;
            leftHeadlightGroup.add(leftHousing);

            // LED灯条
            for (let i = 0; i < 3; i++) {
                const led = new THREE.Mesh(ledGeometry, ledMaterial);
                led.position.set(0.12, -0.05 + i * 0.05, 0);
                leftHeadlightGroup.add(led);
            }

            leftHeadlightGroup.position.set(1.95, 0.4, 0.65);
            headlights.push(leftHeadlightGroup);
            carGroup.add(leftHeadlightGroup);

            // 右前灯组
            const rightHeadlightGroup = leftHeadlightGroup.clone();
            rightHeadlightGroup.position.set(1.95, 0.4, -0.65);
            headlights.push(rightHeadlightGroup);
            carGroup.add(rightHeadlightGroup);

            // 现代尾灯设计
            const tailLightGeometry = new THREE.BoxGeometry(0.08, 0.3, 0.15);
            const tailLightMaterial = new THREE.MeshPhysicalMaterial({
                color: 0xff2222,
                emissive: 0x220000,
                emissiveIntensity: 0.2,
                transparent: true,
                opacity: 0.8
            });

            // 左尾灯
            const leftTaillight = new THREE.Mesh(tailLightGeometry, tailLightMaterial);
            leftTaillight.position.set(-1.98, 0.4, 0.7);
            carGroup.add(leftTaillight);

            // 右尾灯
            const rightTaillight = new THREE.Mesh(tailLightGeometry, tailLightMaterial);
            rightTaillight.position.set(-1.98, 0.4, -0.7);
            carGroup.add(rightTaillight);

            // 转向灯
            const turnSignalGeometry = new THREE.SphereGeometry(0.08, 16, 16);
            const turnSignalMaterial = new THREE.MeshPhongMaterial({
                color: 0xffaa00,
                emissive: 0x442200,
                transparent: true,
                opacity: 0.7
            });

            // 前转向灯
            const frontLeftTurn = new THREE.Mesh(turnSignalGeometry, turnSignalMaterial);
            frontLeftTurn.position.set(1.9, 0.2, 0.8);
            carGroup.add(frontLeftTurn);

            const frontRightTurn = new THREE.Mesh(turnSignalGeometry, turnSignalMaterial);
            frontRightTurn.position.set(1.9, 0.2, -0.8);
            carGroup.add(frontRightTurn);
        }

        function createDetails() {
            // 前进气格栅
            const grilleGeometry = new THREE.BoxGeometry(0.05, 0.4, 1.2);
            const grilleMaterial = new THREE.MeshPhysicalMaterial({
                color: 0x1a1a1a,
                metalness: 0.8,
                roughness: 0.3
            });

            const grille = new THREE.Mesh(grilleGeometry, grilleMaterial);
            grille.position.set(2.02, 0.5, 0);
            carGroup.add(grille);

            // 格栅条纹
            for (let i = 0; i < 8; i++) {
                const grilleBar = new THREE.Mesh(
                    new THREE.BoxGeometry(0.02, 0.02, 1.0),
                    new THREE.MeshPhongMaterial({ color: 0x666666 })
                );
                grilleBar.position.set(2.03, 0.3 + i * 0.05, 0);
                carGroup.add(grilleBar);
            }

            // 品牌标志
            const logoGeometry = new THREE.CylinderGeometry(0.08, 0.08, 0.02, 16);
            const logoMaterial = new THREE.MeshPhysicalMaterial({
                color: 0xc0c0c0,
                metalness: 0.9,
                roughness: 0.1
            });
            const logo = new THREE.Mesh(logoGeometry, logoMaterial);
            logo.position.set(2.04, 0.5, 0);
            logo.rotation.z = Math.PI / 2;
            carGroup.add(logo);

            // 前保险杠 - 更复杂的形状
            const bumperShape = new THREE.Shape();
            bumperShape.moveTo(-2.1, 0);
            bumperShape.quadraticCurveTo(-2.1, -0.1, -2.0, -0.1);
            bumperShape.lineTo(2.0, -0.1);
            bumperShape.quadraticCurveTo(2.1, -0.1, 2.1, 0);
            bumperShape.lineTo(2.1, 0.1);
            bumperShape.lineTo(-2.1, 0.1);
            bumperShape.lineTo(-2.1, 0);

            const bumperGeometry = new THREE.ExtrudeGeometry(bumperShape, {
                depth: 1.9,
                bevelEnabled: true,
                bevelSize: 0.02,
                bevelThickness: 0.02
            });

            const bumperMaterial = new THREE.MeshPhysicalMaterial({
                color: 0x2a2a2a,
                metalness: 0.3,
                roughness: 0.7
            });

            const frontBumper = new THREE.Mesh(bumperGeometry, bumperMaterial);
            frontBumper.position.set(0, 0.15, -0.95);
            frontBumper.castShadow = true;
            carGroup.add(frontBumper);

            // 排气管
            const exhaustGeometry = new THREE.CylinderGeometry(0.06, 0.06, 0.3, 16);
            const exhaustMaterial = new THREE.MeshPhysicalMaterial({
                color: 0x444444,
                metalness: 0.8,
                roughness: 0.4
            });

            const leftExhaust = new THREE.Mesh(exhaustGeometry, exhaustMaterial);
            leftExhaust.position.set(-1.9, 0.1, 0.4);
            leftExhaust.rotation.z = Math.PI / 2;
            carGroup.add(leftExhaust);

            const rightExhaust = new THREE.Mesh(exhaustGeometry, exhaustMaterial);
            rightExhaust.position.set(-1.9, 0.1, -0.4);
            rightExhaust.rotation.z = Math.PI / 2;
            carGroup.add(rightExhaust);

            // 车牌
            const plateGeometry = new THREE.BoxGeometry(0.52, 0.11, 0.02);
            const plateMaterial = new THREE.MeshPhongMaterial({ color: 0xffffff });
            const frontPlate = new THREE.Mesh(plateGeometry, plateMaterial);
            frontPlate.position.set(2.05, 0.25, 0);
            carGroup.add(frontPlate);

            // 车牌文字（简化）
            const textGeometry = new THREE.BoxGeometry(0.4, 0.05, 0.005);
            const textMaterial = new THREE.MeshPhongMaterial({ color: 0x000000 });
            const plateText = new THREE.Mesh(textGeometry, textMaterial);
            plateText.position.set(2.06, 0.25, 0);
            carGroup.add(plateText);

            // 后视镜 - 更真实的设计
            const mirrorHousingGeometry = new THREE.BoxGeometry(0.12, 0.08, 0.06);
            const mirrorHousingMaterial = new THREE.MeshPhongMaterial({ color: 0x000000 });

            const mirrorGlassGeometry = new THREE.PlaneGeometry(0.08, 0.06);
            const mirrorGlassMaterial = new THREE.MeshPhysicalMaterial({
                color: 0x87CEEB,
                metalness: 0.9,
                roughness: 0.1,
                transparent: true,
                opacity: 0.8
            });

            // 左后视镜
            const leftMirrorGroup = new THREE.Group();
            const leftMirrorHousing = new THREE.Mesh(mirrorHousingGeometry, mirrorHousingMaterial);
            const leftMirrorGlass = new THREE.Mesh(mirrorGlassGeometry, mirrorGlassMaterial);
            leftMirrorGlass.position.set(0.06, 0, 0);
            leftMirrorGroup.add(leftMirrorHousing);
            leftMirrorGroup.add(leftMirrorGlass);
            leftMirrorGroup.position.set(0.8, 1.1, 1.0);
            carGroup.add(leftMirrorGroup);

            // 右后视镜
            const rightMirrorGroup = leftMirrorGroup.clone();
            rightMirrorGroup.position.set(0.8, 1.1, -1.0);
            carGroup.add(rightMirrorGroup);

            // 门把手
            const handleGeometry = new THREE.BoxGeometry(0.15, 0.03, 0.02);
            const handleMaterial = new THREE.MeshPhysicalMaterial({
                color: 0x888888,
                metalness: 0.8,
                roughness: 0.2
            });

            const leftHandle = new THREE.Mesh(handleGeometry, handleMaterial);
            leftHandle.position.set(0.3, 0.5, 0.92);
            carGroup.add(leftHandle);

            const rightHandle = new THREE.Mesh(handleGeometry, handleMaterial);
            rightHandle.position.set(0.3, 0.5, -0.92);
            carGroup.add(rightHandle);
        }

        function createEnvironment() {
            // 更真实的地面
            const groundGeometry = new THREE.PlaneGeometry(100, 100);
            const groundMaterial = new THREE.MeshPhysicalMaterial({
                color: 0x404040,
                roughness: 0.8,
                metalness: 0.1
            });
            const ground = new THREE.Mesh(groundGeometry, groundMaterial);
            ground.rotation.x = -Math.PI / 2;
            ground.position.y = -0.5;
            ground.receiveShadow = true;
            scene.add(ground);

            // 展厅背景
            const wallGeometry = new THREE.PlaneGeometry(30, 15);
            const wallMaterial = new THREE.MeshPhysicalMaterial({
                color: 0xf0f0f0,
                roughness: 0.9,
                metalness: 0.0
            });

            const backWall = new THREE.Mesh(wallGeometry, wallMaterial);
            backWall.position.set(-15, 7, 0);
            backWall.rotation.y = Math.PI / 2;
            backWall.receiveShadow = true;
            scene.add(backWall);

            // 天花板
            const ceilingGeometry = new THREE.PlaneGeometry(30, 30);
            const ceilingMaterial = new THREE.MeshPhysicalMaterial({
                color: 0xe0e0e0,
                roughness: 0.7,
                metalness: 0.0
            });
            const ceiling = new THREE.Mesh(ceilingGeometry, ceilingMaterial);
            ceiling.position.y = 15;
            ceiling.rotation.x = Math.PI / 2;
            scene.add(ceiling);

            // 添加一些装饰柱子
            const pillarGeometry = new THREE.CylinderGeometry(0.3, 0.3, 15, 16);
            const pillarMaterial = new THREE.MeshPhysicalMaterial({
                color: 0xcccccc,
                roughness: 0.3,
                metalness: 0.1
            });

            const positions = [
                { x: -10, z: -10 },
                { x: -10, z: 10 },
                { x: 10, z: -10 },
                { x: 10, z: 10 }
            ];

            positions.forEach(pos => {
                const pillar = new THREE.Mesh(pillarGeometry, pillarMaterial);
                pillar.position.set(pos.x, 7.5, pos.z);
                pillar.castShadow = true;
                pillar.receiveShadow = true;
                scene.add(pillar);
            });
        }

        function createLights() {
            // 主光源 - 模拟展厅顶灯
            const mainLight = new THREE.DirectionalLight(0xffffff, 1.2);
            mainLight.position.set(5, 12, 3);
            mainLight.castShadow = true;
            mainLight.shadow.mapSize.width = 4096;
            mainLight.shadow.mapSize.height = 4096;
            mainLight.shadow.camera.near = 0.5;
            mainLight.shadow.camera.far = 50;
            mainLight.shadow.camera.left = -15;
            mainLight.shadow.camera.right = 15;
            mainLight.shadow.camera.top = 15;
            mainLight.shadow.camera.bottom = -15;
            mainLight.shadow.bias = -0.0001;
            scene.add(mainLight);

            // 辅助光源
            const fillLight = new THREE.DirectionalLight(0xffffff, 0.3);
            fillLight.position.set(-5, 8, -3);
            scene.add(fillLight);

            // 环境光
            const ambientLight = new THREE.AmbientLight(0x404040, 0.3);
            scene.add(ambientLight);

            // 半球光 - 模拟天空光
            const hemisphereLight = new THREE.HemisphereLight(0xb1e1ff, 0xb97a20, 0.4);
            scene.add(hemisphereLight);

            // 聚光灯 - 突出汽车
            const spotLight1 = new THREE.SpotLight(0xffffff, 0.8);
            spotLight1.position.set(8, 10, 8);
            spotLight1.target.position.set(0, 0, 0);
            spotLight1.angle = Math.PI / 6;
            spotLight1.penumbra = 0.3;
            spotLight1.decay = 2;
            spotLight1.distance = 30;
            spotLight1.castShadow = true;
            scene.add(spotLight1);
            scene.add(spotLight1.target);

            const spotLight2 = new THREE.SpotLight(0xffffff, 0.6);
            spotLight2.position.set(-8, 10, -8);
            spotLight2.target.position.set(0, 0, 0);
            spotLight2.angle = Math.PI / 6;
            spotLight2.penumbra = 0.3;
            spotLight2.decay = 2;
            spotLight2.distance = 30;
            scene.add(spotLight2);
            scene.add(spotLight2.target);

            // 地面反射光
            const groundLight = new THREE.DirectionalLight(0x4444ff, 0.2);
            groundLight.position.set(0, -1, 0);
            scene.add(groundLight);
        }

        function animate() {
            requestAnimationFrame(animate);

            // 自动旋转
            if (isRotating && carGroup) {
                carGroup.rotation.y += 0.01;
            }

            // 轮子旋转
            if (isRotating) {
                wheels.forEach(wheel => {
                    wheel.rotation.x += 0.1;
                });
            }

            // 更新控制器
            controls.update();

            // 渲染场景
            renderer.render(scene, camera);
        }

        // 控制函数
        function toggleRotation() {
            isRotating = !isRotating;
        }

        function resetView() {
            camera.position.set(5, 3, 5);
            controls.reset();
        }

        function toggleWireframe() {
            isWireframe = !isWireframe;
            carGroup.traverse((child) => {
                if (child.isMesh && child.material) {
                    child.material.wireframe = isWireframe;
                }
            });
        }

        function toggleLights() {
            lightsOn = !lightsOn;
            headlights.forEach(light => {
                if (lightsOn) {
                    light.material.emissive.setHex(0x888800);
                    light.material.emissiveIntensity = 0.5;
                } else {
                    light.material.emissive.setHex(0x444400);
                    light.material.emissiveIntensity = 0.1;
                }
            });
        }

        function openDoors() {
            doorsOpen = !doorsOpen;
            doors.forEach((door, index) => {
                if (doorsOpen) {
                    door.rotation.y = index === 0 ? Math.PI / 3 : -Math.PI / 3;
                } else {
                    door.rotation.y = 0;
                }
            });
        }

        function changeCarColor(color) {
            if (carBody) {
                carBody.material.color.setStyle(color);
            }
            doors.forEach(door => {
                door.material.color.setStyle(color);
            });
        }

        function changeRoofColor(color) {
            if (carRoof) {
                carRoof.material.color.setStyle(color);
            }
        }

        // 窗口大小调整
        window.addEventListener('resize', () => {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        });

        // 将函数暴露到全局作用域
        window.toggleRotation = toggleRotation;
        window.resetView = resetView;
        window.toggleWireframe = toggleWireframe;
        window.toggleLights = toggleLights;
        window.openDoors = openDoors;
        window.changeCarColor = changeCarColor;
        window.changeRoofColor = changeRoofColor;

        // 初始化
        init();
    </script>
</body>
</html>
