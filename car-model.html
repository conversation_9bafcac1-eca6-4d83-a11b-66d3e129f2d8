<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D汽车模型</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: Arial, sans-serif;
            overflow: hidden;
        }
        
        #container {
            width: 100vw;
            height: 100vh;
            position: relative;
        }
        
        #info {
            position: absolute;
            top: 20px;
            left: 20px;
            color: white;
            z-index: 100;
            background: rgba(0, 0, 0, 0.7);
            padding: 15px;
            border-radius: 8px;
            font-size: 14px;
            max-width: 300px;
        }
        
        #controls {
            position: absolute;
            bottom: 20px;
            left: 20px;
            color: white;
            z-index: 100;
            background: rgba(0, 0, 0, 0.7);
            padding: 15px;
            border-radius: 8px;
        }
        
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 8px 16px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
            transition: background 0.3s;
        }
        
        button:hover {
            background: #45a049;
        }
        
        #colorPicker {
            position: absolute;
            bottom: 20px;
            right: 20px;
            color: white;
            z-index: 100;
            background: rgba(0, 0, 0, 0.7);
            padding: 15px;
            border-radius: 8px;
        }
        
        input[type="color"] {
            width: 40px;
            height: 40px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        
        #loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 18px;
            z-index: 200;
        }
    </style>
</head>
<body>
    <div id="container">
        <div id="loading">加载中...</div>
        <div id="info">
            <h3>3D汽车模型</h3>
            <p>• 完整的汽车结构设计</p>
            <p>• 鼠标拖拽旋转视角</p>
            <p>• 滚轮缩放</p>
            <p>• 实时光照和阴影</p>
            <p>• 可自定义车身颜色</p>
        </div>
        <div id="controls">
            <button onclick="toggleRotation()">自动旋转</button>
            <button onclick="resetView()">重置视角</button>
            <button onclick="toggleWireframe()">线框模式</button>
            <button onclick="toggleLights()">车灯开关</button>
            <button onclick="openDoors()">开关车门</button>
        </div>
        <div id="colorPicker">
            <h4>车身颜色:</h4>
            <input type="color" id="bodyColor" value="#ff0000" onchange="changeCarColor(this.value)">
            <input type="color" id="roofColor" value="#000000" onchange="changeRoofColor(this.value)">
            <br>
            <label style="font-size: 12px;">车身 | 车顶</label>
        </div>
    </div>

    <script type="importmap">
    {
        "imports": {
            "three": "https://unpkg.com/three@0.155.0/build/three.module.js",
            "three/addons/": "https://unpkg.com/three@0.155.0/examples/jsm/"
        }
    }
    </script>
    
    <script type="module">
        import * as THREE from 'three';
        import { OrbitControls } from 'three/addons/controls/OrbitControls.js';
        
        let scene, camera, renderer, car, carGroup;
        let controls;
        let isRotating = false;
        let isWireframe = false;
        let lightsOn = false;
        let doorsOpen = false;
        
        // 汽车部件
        let carBody, carRoof, wheels = [], doors = [], headlights = [];
        
        function init() {
            // 创建场景
            scene = new THREE.Scene();
            scene.fog = new THREE.Fog(0x667eea, 10, 50);
            
            // 创建相机
            camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
            camera.position.set(5, 3, 5);
            
            // 创建渲染器
            renderer = new THREE.WebGLRenderer({ antialias: true });
            renderer.setSize(window.innerWidth, window.innerHeight);
            renderer.shadowMap.enabled = true;
            renderer.shadowMap.type = THREE.PCFSoftShadowMap;
            renderer.setClearColor(0x667eea, 1);
            renderer.physicallyCorrectLights = true;
            document.getElementById('container').appendChild(renderer.domElement);
            
            // 创建控制器
            controls = new OrbitControls(camera, renderer.domElement);
            controls.enableDamping = true;
            controls.dampingFactor = 0.05;
            controls.minDistance = 3;
            controls.maxDistance = 20;
            controls.maxPolarAngle = Math.PI / 2;
            
            // 创建汽车
            createCar();
            
            // 创建环境
            createEnvironment();
            
            // 创建光照
            createLights();
            
            // 开始渲染
            animate();
            
            // 隐藏加载提示
            document.getElementById('loading').style.display = 'none';
        }
        
        function createCar() {
            carGroup = new THREE.Group();
            
            // 创建车身主体
            createCarBody();
            
            // 创建车顶
            createCarRoof();
            
            // 创建轮子
            createWheels();
            
            // 创建车窗
            createWindows();
            
            // 创建车门
            createDoors();
            
            // 创建车灯
            createHeadlights();
            
            // 创建细节
            createDetails();
            
            scene.add(carGroup);
        }
        
        function createCarBody() {
            const bodyGeometry = new THREE.BoxGeometry(4, 0.8, 1.8);
            const bodyMaterial = new THREE.MeshPhongMaterial({ 
                color: 0xff0000,
                shininess: 100,
                specular: 0x111111
            });
            carBody = new THREE.Mesh(bodyGeometry, bodyMaterial);
            carBody.position.y = 0.4;
            carBody.castShadow = true;
            carBody.receiveShadow = true;
            carGroup.add(carBody);
        }
        
        function createCarRoof() {
            const roofGeometry = new THREE.BoxGeometry(2.5, 0.8, 1.6);
            const roofMaterial = new THREE.MeshPhongMaterial({ 
                color: 0x000000,
                shininess: 100
            });
            carRoof = new THREE.Mesh(roofGeometry, roofMaterial);
            carRoof.position.set(-0.2, 1.2, 0);
            carRoof.castShadow = true;
            carGroup.add(carRoof);
        }
        
        function createWheels() {
            const wheelGeometry = new THREE.CylinderGeometry(0.4, 0.4, 0.3, 16);
            const wheelMaterial = new THREE.MeshPhongMaterial({ color: 0x333333 });
            const rimMaterial = new THREE.MeshPhongMaterial({ color: 0x888888 });
            
            const wheelPositions = [
                { x: 1.3, y: 0, z: 1.1 },
                { x: 1.3, y: 0, z: -1.1 },
                { x: -1.3, y: 0, z: 1.1 },
                { x: -1.3, y: 0, z: -1.1 }
            ];
            
            wheelPositions.forEach((pos, index) => {
                const wheelGroup = new THREE.Group();
                
                // 轮胎
                const wheel = new THREE.Mesh(wheelGeometry, wheelMaterial);
                wheel.rotation.z = Math.PI / 2;
                wheel.castShadow = true;
                wheelGroup.add(wheel);
                
                // 轮毂
                const rimGeometry = new THREE.CylinderGeometry(0.25, 0.25, 0.32, 8);
                const rim = new THREE.Mesh(rimGeometry, rimMaterial);
                rim.rotation.z = Math.PI / 2;
                wheelGroup.add(rim);
                
                wheelGroup.position.set(pos.x, pos.y, pos.z);
                wheels.push(wheelGroup);
                carGroup.add(wheelGroup);
            });
        }
        
        function createWindows() {
            const windowMaterial = new THREE.MeshPhongMaterial({ 
                color: 0x87CEEB,
                transparent: true,
                opacity: 0.3,
                side: THREE.DoubleSide
            });
            
            // 前挡风玻璃
            const frontWindowGeometry = new THREE.PlaneGeometry(2.4, 0.6);
            const frontWindow = new THREE.Mesh(frontWindowGeometry, windowMaterial);
            frontWindow.position.set(0.8, 1.2, 0);
            frontWindow.rotation.y = Math.PI / 2;
            frontWindow.rotation.x = -0.2;
            carGroup.add(frontWindow);
            
            // 后挡风玻璃
            const rearWindow = new THREE.Mesh(frontWindowGeometry, windowMaterial);
            rearWindow.position.set(-1.2, 1.2, 0);
            rearWindow.rotation.y = Math.PI / 2;
            rearWindow.rotation.x = 0.2;
            carGroup.add(rearWindow);
            
            // 侧窗
            const sideWindowGeometry = new THREE.PlaneGeometry(1.2, 0.6);
            const leftWindow = new THREE.Mesh(sideWindowGeometry, windowMaterial);
            leftWindow.position.set(-0.2, 1.2, 0.81);
            carGroup.add(leftWindow);
            
            const rightWindow = new THREE.Mesh(sideWindowGeometry, windowMaterial);
            rightWindow.position.set(-0.2, 1.2, -0.81);
            carGroup.add(rightWindow);
        }

        function createDoors() {
            const doorGeometry = new THREE.BoxGeometry(1.2, 0.8, 0.1);
            const doorMaterial = new THREE.MeshPhongMaterial({ color: 0xff0000 });

            // 左门
            const leftDoor = new THREE.Mesh(doorGeometry, doorMaterial);
            leftDoor.position.set(0.3, 0.4, 0.95);
            leftDoor.castShadow = true;
            doors.push(leftDoor);
            carGroup.add(leftDoor);

            // 右门
            const rightDoor = new THREE.Mesh(doorGeometry, doorMaterial);
            rightDoor.position.set(0.3, 0.4, -0.95);
            rightDoor.castShadow = true;
            doors.push(rightDoor);
            carGroup.add(rightDoor);
        }

        function createHeadlights() {
            const lightGeometry = new THREE.SphereGeometry(0.15, 16, 16);
            const lightMaterial = new THREE.MeshPhongMaterial({
                color: 0xffffaa,
                emissive: 0x444400
            });

            // 左前灯
            const leftHeadlight = new THREE.Mesh(lightGeometry, lightMaterial);
            leftHeadlight.position.set(1.9, 0.3, 0.6);
            headlights.push(leftHeadlight);
            carGroup.add(leftHeadlight);

            // 右前灯
            const rightHeadlight = new THREE.Mesh(lightGeometry, lightMaterial);
            rightHeadlight.position.set(1.9, 0.3, -0.6);
            headlights.push(rightHeadlight);
            carGroup.add(rightHeadlight);

            // 尾灯
            const tailLightMaterial = new THREE.MeshPhongMaterial({
                color: 0xff4444,
                emissive: 0x440000
            });

            const leftTaillight = new THREE.Mesh(lightGeometry, tailLightMaterial);
            leftTaillight.position.set(-1.9, 0.3, 0.6);
            carGroup.add(leftTaillight);

            const rightTaillight = new THREE.Mesh(lightGeometry, tailLightMaterial);
            rightTaillight.position.set(-1.9, 0.3, -0.6);
            carGroup.add(rightTaillight);
        }

        function createDetails() {
            // 前保险杠
            const bumperGeometry = new THREE.BoxGeometry(4.2, 0.2, 1.9);
            const bumperMaterial = new THREE.MeshPhongMaterial({ color: 0x333333 });
            const frontBumper = new THREE.Mesh(bumperGeometry, bumperMaterial);
            frontBumper.position.set(0, 0.1, 0);
            frontBumper.castShadow = true;
            carGroup.add(frontBumper);

            // 后保险杠
            const rearBumper = new THREE.Mesh(bumperGeometry, bumperMaterial);
            rearBumper.position.set(0, 0.1, 0);
            rearBumper.castShadow = true;
            carGroup.add(rearBumper);

            // 车牌
            const plateGeometry = new THREE.PlaneGeometry(0.6, 0.3);
            const plateMaterial = new THREE.MeshPhongMaterial({ color: 0xffffff });
            const frontPlate = new THREE.Mesh(plateGeometry, plateMaterial);
            frontPlate.position.set(2.05, 0.2, 0);
            frontPlate.rotation.y = Math.PI / 2;
            carGroup.add(frontPlate);

            // 后视镜
            const mirrorGeometry = new THREE.BoxGeometry(0.1, 0.1, 0.15);
            const mirrorMaterial = new THREE.MeshPhongMaterial({ color: 0x000000 });

            const leftMirror = new THREE.Mesh(mirrorGeometry, mirrorMaterial);
            leftMirror.position.set(0.8, 1.0, 1.0);
            carGroup.add(leftMirror);

            const rightMirror = new THREE.Mesh(mirrorGeometry, mirrorMaterial);
            rightMirror.position.set(0.8, 1.0, -1.0);
            carGroup.add(rightMirror);
        }

        function createEnvironment() {
            // 地面
            const groundGeometry = new THREE.PlaneGeometry(50, 50);
            const groundMaterial = new THREE.MeshLambertMaterial({ color: 0x999999 });
            const ground = new THREE.Mesh(groundGeometry, groundMaterial);
            ground.rotation.x = -Math.PI / 2;
            ground.position.y = -0.5;
            ground.receiveShadow = true;
            scene.add(ground);

            // 网格线
            const gridHelper = new THREE.GridHelper(50, 50, 0x666666, 0x666666);
            gridHelper.position.y = -0.49;
            scene.add(gridHelper);
        }

        function createLights() {
            // 主光源
            const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
            directionalLight.position.set(10, 10, 5);
            directionalLight.castShadow = true;
            directionalLight.shadow.mapSize.width = 2048;
            directionalLight.shadow.mapSize.height = 2048;
            directionalLight.shadow.camera.near = 0.5;
            directionalLight.shadow.camera.far = 50;
            directionalLight.shadow.camera.left = -10;
            directionalLight.shadow.camera.right = 10;
            directionalLight.shadow.camera.top = 10;
            directionalLight.shadow.camera.bottom = -10;
            scene.add(directionalLight);

            // 环境光
            const ambientLight = new THREE.AmbientLight(0x404040, 0.4);
            scene.add(ambientLight);

            // 半球光
            const hemisphereLight = new THREE.HemisphereLight(0x87CEEB, 0x362204, 0.6);
            scene.add(hemisphereLight);
        }

        function animate() {
            requestAnimationFrame(animate);

            // 自动旋转
            if (isRotating && carGroup) {
                carGroup.rotation.y += 0.01;
            }

            // 轮子旋转
            if (isRotating) {
                wheels.forEach(wheel => {
                    wheel.rotation.x += 0.1;
                });
            }

            // 更新控制器
            controls.update();

            // 渲染场景
            renderer.render(scene, camera);
        }

        // 控制函数
        function toggleRotation() {
            isRotating = !isRotating;
        }

        function resetView() {
            camera.position.set(5, 3, 5);
            controls.reset();
        }

        function toggleWireframe() {
            isWireframe = !isWireframe;
            carGroup.traverse((child) => {
                if (child.isMesh && child.material) {
                    child.material.wireframe = isWireframe;
                }
            });
        }

        function toggleLights() {
            lightsOn = !lightsOn;
            headlights.forEach(light => {
                if (lightsOn) {
                    light.material.emissive.setHex(0x888800);
                    light.material.emissiveIntensity = 0.5;
                } else {
                    light.material.emissive.setHex(0x444400);
                    light.material.emissiveIntensity = 0.1;
                }
            });
        }

        function openDoors() {
            doorsOpen = !doorsOpen;
            doors.forEach((door, index) => {
                if (doorsOpen) {
                    door.rotation.y = index === 0 ? Math.PI / 3 : -Math.PI / 3;
                } else {
                    door.rotation.y = 0;
                }
            });
        }

        function changeCarColor(color) {
            if (carBody) {
                carBody.material.color.setStyle(color);
            }
            doors.forEach(door => {
                door.material.color.setStyle(color);
            });
        }

        function changeRoofColor(color) {
            if (carRoof) {
                carRoof.material.color.setStyle(color);
            }
        }

        // 窗口大小调整
        window.addEventListener('resize', () => {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        });

        // 将函数暴露到全局作用域
        window.toggleRotation = toggleRotation;
        window.resetView = resetView;
        window.toggleWireframe = toggleWireframe;
        window.toggleLights = toggleLights;
        window.openDoors = openDoors;
        window.changeCarColor = changeCarColor;
        window.changeRoofColor = changeRoofColor;

        // 初始化
        init();
    </script>
</body>
</html>
