<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>精确地球可视化</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #000;
            font-family: Arial, sans-serif;
            overflow: hidden;
        }
        
        #container {
            width: 100vw;
            height: 100vh;
            position: relative;
        }
        
        #info {
            position: absolute;
            top: 20px;
            left: 20px;
            color: white;
            z-index: 100;
            background: rgba(0, 0, 0, 0.7);
            padding: 15px;
            border-radius: 8px;
            font-size: 14px;
            max-width: 300px;
        }
        
        #controls {
            position: absolute;
            bottom: 20px;
            left: 20px;
            color: white;
            z-index: 100;
            background: rgba(0, 0, 0, 0.7);
            padding: 15px;
            border-radius: 8px;
        }
        
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 8px 16px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        
        button:hover {
            background: #45a049;
        }
        
        #loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 18px;
            z-index: 200;
        }
    </style>
</head>
<body>
    <div id="container">
        <div id="loading">加载中...</div>
        <div id="info">
            <h3>太阳系行星可视化</h3>
            <p>• 完整的太阳系行星系统</p>
            <p>• 真实的轨道和大小比例</p>
            <p>• 鼠标拖拽旋转视角</p>
            <p>• 滚轮缩放</p>
            <p>• 实时光照效果</p>
            <div id="coordinates">视角: 0°, 0°</div>
            <div id="planetInfo">当前行星: 地球</div>
        </div>
        <div id="controls">
            <button onclick="toggleRotation()">切换自转</button>
            <button onclick="toggleOrbits()">显示轨道</button>
            <button onclick="resetView()">重置视角</button>
            <button onclick="toggleWireframe()">线框模式</button>
            <button onclick="toggleLighting()">光照效果</button>
            <button onclick="speedUp()">加速时间</button>
            <button onclick="slowDown()">减速时间</button>
        </div>
        <div id="planetSelector" style="position: absolute; bottom: 80px; left: 20px; color: white; background: rgba(0, 0, 0, 0.7); padding: 15px; border-radius: 8px;">
            <h4>选择行星:</h4>
            <button onclick="focusPlanet('sun')">太阳</button>
            <button onclick="focusPlanet('mercury')">水星</button>
            <button onclick="focusPlanet('venus')">金星</button>
            <button onclick="focusPlanet('earth')">地球</button>
            <button onclick="focusPlanet('mars')">火星</button>
            <button onclick="focusPlanet('jupiter')">木星</button>
            <button onclick="focusPlanet('saturn')">土星</button>
            <button onclick="focusPlanet('uranus')">天王星</button>
            <button onclick="focusPlanet('neptune')">海王星</button>
        </div>
    </div>

    <script type="importmap">
    {
        "imports": {
            "three": "https://unpkg.com/three@0.155.0/build/three.module.js",
            "three/addons/": "https://unpkg.com/three@0.155.0/examples/jsm/"
        }
    }
    </script>

    <script type="module">
        import * as THREE from 'three';
        import { OrbitControls } from 'three/addons/controls/OrbitControls.js';

        let scene, camera, renderer;
        let controls;
        let directionalLight, ambientLight, hemisphereLight;
        let isRotating = true;
        let isWireframe = false;
        let lightingEnabled = true;
        let showOrbits = true;
        let timeSpeed = 1;

        // 太阳系对象
        let sun, planets = {}, orbits = [];
        let currentFocus = 'earth';

        // 行星数据 (相对大小和距离，已调整为可视化友好的比例)
        const planetData = {
            sun: { radius: 0.5, distance: 0, color: 0xFDB813, rotationSpeed: 0.001, orbitSpeed: 0 },
            mercury: { radius: 0.03, distance: 2, color: 0x8C7853, rotationSpeed: 0.01, orbitSpeed: 0.02 },
            venus: { radius: 0.05, distance: 3, color: 0xFFC649, rotationSpeed: 0.008, orbitSpeed: 0.015 },
            earth: { radius: 0.06, distance: 4, color: 0x6B93D6, rotationSpeed: 0.005, orbitSpeed: 0.01 },
            mars: { radius: 0.04, distance: 5, color: 0xCD5C5C, rotationSpeed: 0.005, orbitSpeed: 0.008 },
            jupiter: { radius: 0.25, distance: 8, color: 0xD8CA9D, rotationSpeed: 0.002, orbitSpeed: 0.004 },
            saturn: { radius: 0.2, distance: 12, color: 0xFAD5A5, rotationSpeed: 0.002, orbitSpeed: 0.003 },
            uranus: { radius: 0.12, distance: 16, color: 0x4FD0E7, rotationSpeed: 0.003, orbitSpeed: 0.002 },
            neptune: { radius: 0.11, distance: 20, color: 0x4B70DD, rotationSpeed: 0.003, orbitSpeed: 0.001 }
        };
        
        // 渲染参数
        const PLANET_SEGMENTS = 32;
        
        function init() {
            // 创建场景
            scene = new THREE.Scene();
            
            // 创建相机
            camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
            camera.position.set(0, 5, 15);
            
            // 创建渲染器
            renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
            renderer.setSize(window.innerWidth, window.innerHeight);
            renderer.shadowMap.enabled = true;
            renderer.shadowMap.type = THREE.PCFSoftShadowMap;
            renderer.setClearColor(0x000000, 1);
            renderer.gammaOutput = true;
            renderer.gammaFactor = 2.2;
            renderer.physicallyCorrectLights = true;
            document.getElementById('container').appendChild(renderer.domElement);
            
            // 创建控制器
            controls = new OrbitControls(camera, renderer.domElement);
            controls.enableDamping = true;
            controls.dampingFactor = 0.05;
            controls.minDistance = 2;
            controls.maxDistance = 100;

            // 创建太阳系
            createSolarSystem();

            // 创建轨道
            createOrbits();

            // 创建光照
            createLights();

            // 创建星空背景
            createStarField();
            
            // 开始渲染
            animate();
            
            // 隐藏加载提示
            document.getElementById('loading').style.display = 'none';
        }
        
        function createSolarSystem() {
            const textureLoader = new THREE.TextureLoader();

            // 创建太阳
            const sunGeometry = new THREE.SphereGeometry(planetData.sun.radius, PLANET_SEGMENTS, PLANET_SEGMENTS);
            const sunMaterial = new THREE.MeshBasicMaterial({
                color: planetData.sun.color,
                emissive: planetData.sun.color,
                emissiveIntensity: 0.3
            });
            sun = new THREE.Mesh(sunGeometry, sunMaterial);
            sun.position.set(0, 0, 0);
            scene.add(sun);

            // 创建行星
            Object.keys(planetData).forEach(planetName => {
                if (planetName === 'sun') return;

                const data = planetData[planetName];
                const geometry = new THREE.SphereGeometry(data.radius, PLANET_SEGMENTS, PLANET_SEGMENTS);

                // 尝试加载真实纹理，失败则使用颜色
                let material;
                const textureUrl = getPlanetTextureUrl(planetName);

                if (textureUrl) {
                    const texture = textureLoader.load(textureUrl,
                        () => console.log(`${planetName} 纹理加载成功`),
                        undefined,
                        () => {
                            console.log(`${planetName} 纹理加载失败，使用颜色`);
                            planets[planetName].material = new THREE.MeshPhongMaterial({ color: data.color });
                        }
                    );
                    material = new THREE.MeshPhongMaterial({ map: texture });
                } else {
                    material = new THREE.MeshPhongMaterial({ color: data.color });
                }

                const planet = new THREE.Mesh(geometry, material);
                planet.position.set(data.distance, 0, 0);
                planet.castShadow = true;
                planet.receiveShadow = true;

                // 为地球添加大气层
                if (planetName === 'earth') {
                    const atmosphereGeometry = new THREE.SphereGeometry(data.radius * 1.05, PLANET_SEGMENTS, PLANET_SEGMENTS);
                    const atmosphereMaterial = new THREE.MeshPhongMaterial({
                        color: 0x87CEEB,
                        transparent: true,
                        opacity: 0.2,
                        side: THREE.BackSide
                    });
                    const atmosphere = new THREE.Mesh(atmosphereGeometry, atmosphereMaterial);
                    planet.add(atmosphere);
                }

                // 为土星添加光环
                if (planetName === 'saturn') {
                    createSaturnRings(planet, data.radius);
                }

                planets[planetName] = planet;
                scene.add(planet);
            });
        }
        
        function getPlanetTextureUrl(planetName) {
            const textureUrls = {
                mercury: 'https://raw.githubusercontent.com/mrdoob/three.js/dev/examples/textures/planets/mercury_1024.jpg',
                venus: 'https://raw.githubusercontent.com/mrdoob/three.js/dev/examples/textures/planets/venus_surface_1024.jpg',
                earth: 'https://raw.githubusercontent.com/mrdoob/three.js/dev/examples/textures/planets/earth_atmos_2048.jpg',
                mars: 'https://raw.githubusercontent.com/mrdoob/three.js/dev/examples/textures/planets/mars_1024.jpg',
                jupiter: 'https://raw.githubusercontent.com/mrdoob/three.js/dev/examples/textures/planets/jupiter_1024.jpg',
                saturn: 'https://raw.githubusercontent.com/mrdoob/three.js/dev/examples/textures/planets/saturn_1024.jpg',
                uranus: 'https://raw.githubusercontent.com/mrdoob/three.js/dev/examples/textures/planets/uranus_1024.jpg',
                neptune: 'https://raw.githubusercontent.com/mrdoob/three.js/dev/examples/textures/planets/neptune_1024.jpg'
            };
            return textureUrls[planetName];
        }

        function createSaturnRings(planet, planetRadius) {
            const ringGeometry = new THREE.RingGeometry(planetRadius * 1.2, planetRadius * 2, 32);
            const ringMaterial = new THREE.MeshPhongMaterial({
                color: 0xD4AF37,
                transparent: true,
                opacity: 0.7,
                side: THREE.DoubleSide
            });
            const rings = new THREE.Mesh(ringGeometry, ringMaterial);
            rings.rotation.x = Math.PI / 2;
            planet.add(rings);
        }

        function createOrbits() {
            Object.keys(planetData).forEach(planetName => {
                if (planetName === 'sun') return;

                const data = planetData[planetName];
                const orbitGeometry = new THREE.RingGeometry(data.distance - 0.01, data.distance + 0.01, 64);
                const orbitMaterial = new THREE.MeshBasicMaterial({
                    color: 0x444444,
                    transparent: true,
                    opacity: 0.3,
                    side: THREE.DoubleSide
                });
                const orbit = new THREE.Mesh(orbitGeometry, orbitMaterial);
                orbit.rotation.x = Math.PI / 2;
                orbits.push(orbit);
                scene.add(orbit);
            });
        }
        
        function createLights() {
            // 主光源（太阳光）- 增强亮度
            directionalLight = new THREE.DirectionalLight(0xffffff, 3.0);
            directionalLight.position.set(10, 5, 5);
            directionalLight.castShadow = true;
            directionalLight.shadow.mapSize.width = 2048;
            directionalLight.shadow.mapSize.height = 2048;
            directionalLight.shadow.camera.near = 0.1;
            directionalLight.shadow.camera.far = 50;
            directionalLight.shadow.camera.left = -10;
            directionalLight.shadow.camera.right = 10;
            directionalLight.shadow.camera.top = 10;
            directionalLight.shadow.camera.bottom = -10;
            scene.add(directionalLight);

            // 环境光 - 降低强度以增强对比
            ambientLight = new THREE.AmbientLight(0x404040, 0.2);
            scene.add(ambientLight);

            // 添加一个辅助光源来增强对比度
            hemisphereLight = new THREE.HemisphereLight(0x87CEEB, 0x362204, 0.5);
            scene.add(hemisphereLight);

            // 添加光源辅助器（用于显示光源方向）
            const helper = new THREE.DirectionalLightHelper(directionalLight, 1);
            scene.add(helper);
        }
        
        function createStarField() {
            const starsGeometry = new THREE.BufferGeometry();
            const starsMaterial = new THREE.PointsMaterial({ color: 0xffffff, size: 0.5 });
            
            const starsVertices = [];
            for (let i = 0; i < 10000; i++) {
                const x = (Math.random() - 0.5) * 2000;
                const y = (Math.random() - 0.5) * 2000;
                const z = (Math.random() - 0.5) * 2000;
                starsVertices.push(x, y, z);
            }
            
            starsGeometry.setAttribute('position', new THREE.Float32BufferAttribute(starsVertices, 3));
            const stars = new THREE.Points(starsGeometry, starsMaterial);
            scene.add(stars);
        }
        
        function animate() {
            requestAnimationFrame(animate);

            const time = Date.now() * 0.001 * timeSpeed;

            // 太阳自转
            if (sun && isRotating) {
                sun.rotation.y += planetData.sun.rotationSpeed * timeSpeed;
            }

            // 行星运动
            Object.keys(planets).forEach(planetName => {
                const planet = planets[planetName];
                const data = planetData[planetName];

                if (isRotating) {
                    // 行星自转
                    planet.rotation.y += data.rotationSpeed * timeSpeed;

                    // 行星公转
                    const angle = time * data.orbitSpeed;
                    planet.position.x = Math.cos(angle) * data.distance;
                    planet.position.z = Math.sin(angle) * data.distance;
                }
            });

            // 更新控制器
            controls.update();

            // 更新信息显示
            updateInfo();

            // 渲染场景
            renderer.render(scene, camera);
        }
        
        function updateInfo() {
            const vector = new THREE.Vector3();
            camera.getWorldDirection(vector);

            // 计算视角
            const lat = Math.asin(vector.y) * 180 / Math.PI;
            const lng = Math.atan2(vector.x, vector.z) * 180 / Math.PI;

            document.getElementById('coordinates').textContent =
                `视角: ${lat.toFixed(1)}°, ${lng.toFixed(1)}°`;

            // 更新当前关注的行星信息
            const planetNames = {
                sun: '太阳', mercury: '水星', venus: '金星', earth: '地球',
                mars: '火星', jupiter: '木星', saturn: '土星',
                uranus: '天王星', neptune: '海王星'
            };
            document.getElementById('planetInfo').textContent =
                `当前行星: ${planetNames[currentFocus]} | 时间速度: ${timeSpeed}x`;
        }
        
        // 控制函数
        function toggleRotation() {
            isRotating = !isRotating;
        }

        function toggleOrbits() {
            showOrbits = !showOrbits;
            orbits.forEach(orbit => {
                orbit.visible = showOrbits;
            });
        }

        function resetView() {
            camera.position.set(0, 5, 15);
            controls.reset();
            currentFocus = 'earth';
        }

        function toggleWireframe() {
            isWireframe = !isWireframe;
            Object.values(planets).forEach(planet => {
                if (planet && planet.material) {
                    planet.material.wireframe = isWireframe;
                }
            });
            if (sun && sun.material) {
                sun.material.wireframe = isWireframe;
            }
        }

        function toggleLighting() {
            lightingEnabled = !lightingEnabled;
            if (directionalLight) {
                directionalLight.intensity = lightingEnabled ? 3.0 : 0.1;
            }
            if (ambientLight) {
                ambientLight.intensity = lightingEnabled ? 0.2 : 0.8;
            }
            if (hemisphereLight) {
                hemisphereLight.intensity = lightingEnabled ? 0.5 : 0.1;
            }
        }

        function speedUp() {
            timeSpeed = Math.min(timeSpeed * 2, 32);
        }

        function slowDown() {
            timeSpeed = Math.max(timeSpeed / 2, 0.125);
        }

        function focusPlanet(planetName) {
            currentFocus = planetName;
            let target;

            if (planetName === 'sun') {
                target = sun;
            } else {
                target = planets[planetName];
            }

            if (target) {
                const data = planetData[planetName];
                const distance = data.radius * 5 + 2;

                // 平滑移动相机到行星
                const targetPosition = target.position.clone();
                targetPosition.z += distance;
                targetPosition.y += distance * 0.3;

                // 使用动画库或简单的位置设置
                camera.position.copy(targetPosition);
                controls.target.copy(target.position);
                controls.update();
            }
        }

        // 将函数暴露到全局作用域
        window.toggleRotation = toggleRotation;
        window.resetView = resetView;
        window.toggleWireframe = toggleWireframe;
        window.toggleAtmosphere = toggleAtmosphere;
        window.toggleLighting = toggleLighting;
        
        // 窗口大小调整
        window.addEventListener('resize', () => {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        });
        
        // 初始化
        init();
    </script>
</body>
</html>
