<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>精确地球可视化</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #000;
            font-family: Arial, sans-serif;
            overflow: hidden;
        }
        
        #container {
            width: 100vw;
            height: 100vh;
            position: relative;
        }
        
        #info {
            position: absolute;
            top: 20px;
            left: 20px;
            color: white;
            z-index: 100;
            background: rgba(0, 0, 0, 0.7);
            padding: 15px;
            border-radius: 8px;
            font-size: 14px;
            max-width: 300px;
        }
        
        #controls {
            position: absolute;
            bottom: 20px;
            left: 20px;
            color: white;
            z-index: 100;
            background: rgba(0, 0, 0, 0.7);
            padding: 15px;
            border-radius: 8px;
        }
        
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 8px 16px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        
        button:hover {
            background: #45a049;
        }
        
        #loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 18px;
            z-index: 200;
        }
    </style>
</head>
<body>
    <div id="container">
        <div id="loading">加载中...</div>
        <div id="info">
            <h3>精确地球可视化</h3>
            <p>• 使用真实地球纹理数据</p>
            <p>• 鼠标拖拽旋转地球</p>
            <p>• 滚轮缩放</p>
            <p>• 实时光照效果</p>
            <div id="coordinates">坐标: 0°, 0°</div>
        </div>
        <div id="controls">
            <button onclick="toggleRotation()">切换自转</button>
            <button onclick="resetView()">重置视角</button>
            <button onclick="toggleWireframe()">线框模式</button>
            <button onclick="toggleAtmosphere()">大气层</button>
        </div>
    </div>

    <script type="importmap">
    {
        "imports": {
            "three": "https://unpkg.com/three@0.155.0/build/three.module.js",
            "three/addons/": "https://unpkg.com/three@0.155.0/examples/jsm/"
        }
    }
    </script>

    <script type="module">
        import * as THREE from 'three';
        import { OrbitControls } from 'three/addons/controls/OrbitControls.js';

        let scene, camera, renderer, earth, atmosphere;
        let controls;
        let isRotating = true;
        let isWireframe = false;
        let showAtmosphere = true;
        
        // 地球参数
        const EARTH_RADIUS = 1;
        const EARTH_SEGMENTS = 64;
        
        function init() {
            // 创建场景
            scene = new THREE.Scene();
            
            // 创建相机
            camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
            camera.position.set(0, 0, 3);
            
            // 创建渲染器
            renderer = new THREE.WebGLRenderer({ antialias: true });
            renderer.setSize(window.innerWidth, window.innerHeight);
            renderer.shadowMap.enabled = true;
            renderer.shadowMap.type = THREE.PCFSoftShadowMap;
            document.getElementById('container').appendChild(renderer.domElement);
            
            // 创建控制器
            controls = new OrbitControls(camera, renderer.domElement);
            controls.enableDamping = true;
            controls.dampingFactor = 0.05;
            controls.minDistance = 1.5;
            controls.maxDistance = 10;
            
            // 创建地球
            createEarth();
            
            // 创建大气层
            createAtmosphere();
            
            // 创建光照
            createLights();
            
            // 创建星空背景
            createStarField();
            
            // 开始渲染
            animate();
            
            // 隐藏加载提示
            document.getElementById('loading').style.display = 'none';
        }
        
        function createEarth() {
            const geometry = new THREE.SphereGeometry(EARTH_RADIUS, EARTH_SEGMENTS, EARTH_SEGMENTS);

            // 加载地球纹理
            const textureLoader = new THREE.TextureLoader();

            // 使用可靠的地球纹理源
            const earthTexture = textureLoader.load(
                'https://raw.githubusercontent.com/mrdoob/three.js/dev/examples/textures/planets/earth_atmos_2048.jpg',
                function(texture) {
                    console.log('地球纹理加载成功');
                },
                undefined,
                function(error) {
                    console.log('地球纹理加载失败，使用备用纹理');
                    // 使用纯色作为备用
                    earth.material.color = new THREE.Color(0x4169E1);
                }
            );

            const material = new THREE.MeshPhongMaterial({
                map: earthTexture,
                color: 0x4169E1, // 备用颜色
                shininess: 100
            });

            earth = new THREE.Mesh(geometry, material);
            earth.castShadow = true;
            earth.receiveShadow = true;
            scene.add(earth);
        }
        
        function createAtmosphere() {
            const atmosphereGeometry = new THREE.SphereGeometry(EARTH_RADIUS * 1.05, EARTH_SEGMENTS, EARTH_SEGMENTS);
            const atmosphereMaterial = new THREE.MeshPhongMaterial({
                color: 0x87CEEB,
                transparent: true,
                opacity: 0.2,
                side: THREE.BackSide
            });
            
            atmosphere = new THREE.Mesh(atmosphereGeometry, atmosphereMaterial);
            scene.add(atmosphere);
        }
        
        function createLights() {
            // 主光源（太阳光）
            const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
            directionalLight.position.set(5, 3, 5);
            directionalLight.castShadow = true;
            directionalLight.shadow.mapSize.width = 2048;
            directionalLight.shadow.mapSize.height = 2048;
            scene.add(directionalLight);
            
            // 环境光
            const ambientLight = new THREE.AmbientLight(0x404040, 0.3);
            scene.add(ambientLight);
        }
        
        function createStarField() {
            const starsGeometry = new THREE.BufferGeometry();
            const starsMaterial = new THREE.PointsMaterial({ color: 0xffffff, size: 0.5 });
            
            const starsVertices = [];
            for (let i = 0; i < 10000; i++) {
                const x = (Math.random() - 0.5) * 2000;
                const y = (Math.random() - 0.5) * 2000;
                const z = (Math.random() - 0.5) * 2000;
                starsVertices.push(x, y, z);
            }
            
            starsGeometry.setAttribute('position', new THREE.Float32BufferAttribute(starsVertices, 3));
            const stars = new THREE.Points(starsGeometry, starsMaterial);
            scene.add(stars);
        }
        
        function animate() {
            requestAnimationFrame(animate);
            
            // 地球自转
            if (isRotating) {
                earth.rotation.y += 0.005;
                if (atmosphere) {
                    atmosphere.rotation.y += 0.005;
                }
            }
            
            // 更新控制器
            controls.update();
            
            // 更新坐标显示
            updateCoordinates();
            
            // 渲染场景
            renderer.render(scene, camera);
        }
        
        function updateCoordinates() {
            const vector = new THREE.Vector3();
            camera.getWorldDirection(vector);
            
            // 计算经纬度
            const lat = Math.asin(vector.y) * 180 / Math.PI;
            const lng = Math.atan2(vector.x, vector.z) * 180 / Math.PI;
            
            document.getElementById('coordinates').textContent = 
                `视角: ${lat.toFixed(1)}°, ${lng.toFixed(1)}°`;
        }
        
        // 控制函数
        function toggleRotation() {
            isRotating = !isRotating;
        }
        
        function resetView() {
            camera.position.set(0, 0, 3);
            controls.reset();
        }
        
        function toggleWireframe() {
            isWireframe = !isWireframe;
            if (earth && earth.material) {
                earth.material.wireframe = isWireframe;
            }
        }

        function toggleAtmosphere() {
            showAtmosphere = !showAtmosphere;
            if (atmosphere) {
                atmosphere.visible = showAtmosphere;
            }
        }
        
        // 窗口大小调整
        window.addEventListener('resize', () => {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        });
        
        // 初始化
        init();
    </script>
</body>
</html>
